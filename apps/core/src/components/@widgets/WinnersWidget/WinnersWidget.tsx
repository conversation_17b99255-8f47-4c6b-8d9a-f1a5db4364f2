import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import styles from '@widgets/WinnersWidget/WinnersWidget.module.scss'

export const WinnersWidget: FC<IDynamicallyRenderedContentProps> = async () => {
  await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate async data fetching
  return <div className={styles.container}>WinnersWidget</div>
}
