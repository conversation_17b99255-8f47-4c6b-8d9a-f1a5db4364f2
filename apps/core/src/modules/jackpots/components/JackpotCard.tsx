import clsx from 'clsx'
import Image from 'next/image'
import styles from '@modules/jackpots/components/JackpotCard.module.scss'

export interface IJackpotCardProps {
  type: string
  currency: string
  amount: number
  imageSrc?: string
  backgroundImageSrc?: string
  lastWonInfo?: string
}

const JackpotCard = ({ type, currency, amount, imageSrc, backgroundImageSrc, lastWonInfo }: IJackpotCardProps) => {
  return (
    <div className={clsx(styles.jackpotCard, !imageSrc && styles.noIconPadding)}>
      {backgroundImageSrc ? (
        <Image
          src={backgroundImageSrc}
          className={styles.background}
          quality={100}
          fill
          alt={'Jackpot Card background'}
        />
      ) : null}

      {imageSrc ? (
        <Image
          className={styles.image}
          src={imageSrc}
          quality={100}
          alt={type ? `${type} icon` : 'Jackpot Card icon'}
          width={82}
          height={82}
        />
      ) : null}

      <div className={styles.content}>
        {type && currency ? (
          <p className={styles.typeCurrency}>
            {type} {currency}
          </p>
        ) : null}

        {currency && amount !== undefined && amount !== null ? (
          <p className={styles.amount}>
            {currency} {amount.toLocaleString()}
          </p>
        ) : null}

        {lastWonInfo ? <p className={styles.lastWon}>{lastWonInfo}</p> : null}
      </div>
    </div>
  )
}

export default JackpotCard
