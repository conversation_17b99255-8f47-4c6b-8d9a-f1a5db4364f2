@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;
@use '@theme/mixins.scss' as *;

.container {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  gap: calculate-rem(20px);
  background-color: $color-surface-100;
  padding: calculate-rem(16px);
  border-radius: calculate-rem(8px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-radius: calculate-rem(12px);

  @media (max-width: $breakpoint-mobile) {
    flex-direction: column;
    gap: calculate-rem(12px);
    align-items: flex-start;
  }
}

.headerContent {
  display: flex;
  align-items: start;
  gap: calculate-rem(16px);

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(12px);
  }
}

.title {
  font-size: calculate-rem(24px);
  font-weight: 800;
  line-height: calculate-rem(26px);
  letter-spacing: calculate-rem(0.24px);
  color: $color-surface-1000;
  margin-bottom: calculate-rem(8px);

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(18px);
  }
}

.subtitle {
  max-width: 60%;
  font-size: calculate-rem(15px);
  font-weight: 600;
  color: $color-surface-900;
  @include line-clamp(2);

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(12px);
  }
}

.scrollContainer {
  min-width: 100%;
  width: calculate-rem(900px);
  padding: calculate-rem(16px) 0 0 0;
  display: flex;
  gap: calculate-rem(12px);
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  @include custom-scrollbar();

  flex-wrap: nowrap;

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(12px);
  }
}
