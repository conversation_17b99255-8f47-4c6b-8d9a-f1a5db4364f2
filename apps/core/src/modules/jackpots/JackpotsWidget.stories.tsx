import { Locale } from '@constants/locale'
import JackpotsWidget from '@modules/jackpots/JackpotsWidget'
import type { Meta, StoryObj } from '@storybook/react'

const meta: Meta<typeof JackpotsWidget> = {
  title: 'Modules/Jackpots/JackpotsWidget',
  component: JackpotsWidget,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A jackpots widget that displays a header section with title, subtitle, icon, and learn more button',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    locale: Locale.EN,
    config: {
      title: 'LuckyOne Jackpot!',
      subtitle: "Don't miss out! Opt in now to join the jackpot.",
      learnMoreLabel: 'Learn more',
      learnMoreHref: '/jackpots',
      iconSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/info-card-image.png',
    },
  },
}

export default meta
type Story = StoryObj<typeof JackpotsWidget>

export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different variations of the JackpotsWidget component showing various configuration options.',
      },
    },
  },
  render: () => {
    const defaultIcon = 'https://luckyspins-staging4.imgix.net/sweep-rush/info-card-image.png'

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '2rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Configuration Variations</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Default Configuration</h4>
              <JackpotsWidget
                locale={Locale.EN}
                config={{
                  title: 'LuckyOne Jackpot!',
                  subtitle: "Don't miss out! Opt in now to join the jackpot.",
                  learnMoreLabel: 'Learn more',
                  learnMoreHref: '/jackpots',
                  iconSrc: defaultIcon,
                }}
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Custom Title & CTA</h4>
              <JackpotsWidget
                locale={Locale.EN}
                config={{
                  title: 'Progressive Jackpots',
                  subtitle: 'Win big with our progressive jackpot games!',
                  learnMoreLabel: 'Play now',
                  learnMoreHref: '/games',
                  iconSrc: defaultIcon,
                }}
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Without Icon</h4>
              <JackpotsWidget
                locale={Locale.EN}
                config={{
                  title: 'Daily Jackpots',
                  subtitle: "Check out today's jackpot prizes.",
                  learnMoreLabel: 'View all',
                  learnMoreHref: '/jackpots',
                }}
              />
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive JackpotsWidget with all properties customizable via the config object.',
      },
    },
  },
  args: {
    locale: Locale.EN,
    config: {
      title: 'LuckyOne Jackpot!',
      subtitle: "Don't miss out! Opt in now to join the jackpot.",
      learnMoreLabel: 'Learn more',
      learnMoreHref: '/jackpots',
      iconSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/info-card-image.png',
    },
  },
  render: args => (
    <div style={{ maxWidth: '1200px', padding: '20px', background: '#f5f5f5', borderRadius: '8px' }}>
      <JackpotsWidget {...args} />
    </div>
  ),
}
