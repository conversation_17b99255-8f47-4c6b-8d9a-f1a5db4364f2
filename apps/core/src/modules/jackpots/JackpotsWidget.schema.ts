import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedJackpotsConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.JACKPOTS),
  meta: z.object({
    title: z.string().optional(),
    subtitle: z.string().optional(),
    learnMoreLabel: z.string().optional(),
    learnMoreHref: z.string().optional(),
    iconSrc: z.string().url().optional(),
  }),
})

export type DynamicallyRenderedJackpotsConfigType = z.infer<typeof DynamicallyRenderedJackpotsConfigSchema>['meta']
